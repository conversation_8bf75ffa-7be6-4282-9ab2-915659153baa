import { DeepSeek } from "../ai/deepseek";
import ClientSingleton from "../singleton/clientSingleton";

export type DiscordMessage = {
    type: number;
    tts: boolean;
    timestamp: string;
    pinned: boolean;
    nonce: string;
    mentions: any[];
    mention_roles: any[];
    mention_everyone: boolean;
    id: string;
    flags: number;
    embeds: any[];
    edited_timestamp: string | null;
    content: string;
    components: any[];
    channel_type: number;
    channel_id: string;
    author: {
        username: string;
        public_flags: number;
        primary_guild: {
            tag: string;
            identity_guild_id: string;
            identity_enabled: boolean;
            badge: string;
        };
        id: string;
        global_name: string | null;
        discriminator: string;
        collectibles: any | null;
        clan: {
            tag: string;
            identity_guild_id: string;
            identity_enabled: boolean;
            badge: string;
        };
        avatar_decoration_data: {
            sku_id: string;
            expires_at: string | null;
            asset: string;
        };
        avatar: string;
    };
    attachments: any[];
};

export class Conversation {
    private deepseek: DeepSeek;
    private client = ClientSingleton.getClient();

    constructor() {
        this.deepseek = new DeepSeek();
    }

    async onMessage(message: DiscordMessage | any) {
        if (message.author.id === "646342069673263105") return;
        if (message.content.startsWith("!")) return;

        this.saveMessage(message);
        
        const messages = [
            { role: "user", content: message.content }
        ];

        const aiMessage = await this.deepseek.chatCompletion(messages, "Conversation");

        return 0;

        this.client.send(message.channel_id, {
            content: "Thinking...",
            embeds: [],
            allowed_mentions: {
                allowUsers: true,
                allowRoles: true,
                allowEveryone: true,
                allowRepliedUser: true,
            },
            components: null,
            stickers: [],
            reply: null,
            tts: false,
            attachments: []
        });
    }

    async saveMessage(message: DiscordMessage | any) {
        console.log("Saving message:", message.content);

        // Save message on the folder data/converations and "channel_id.json"
        const fs = require('fs');
        const path = `data/conversations/${message.channel_id}.json`;
        if (!fs.existsSync('data/conversations')) {
            fs.mkdirSync('data/conversations', { recursive: true });
        }
        let messages: DiscordMessage[] = [];
        if (fs.existsSync(path)) {
            messages = JSON.parse(fs.readFileSync(path, 'utf8'));
        }
        messages.push(message);
        fs.writeFileSync(path, JSON.stringify(messages));
        console.log("Message saved successfully.");
        return true;
    }
}