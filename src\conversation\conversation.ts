import { DeepSeek } from "../ai/deepseek";
import Client<PERSON>ingleton from "../singleton/clientSingleton";
import { MemoryManager } from "./memory";
import { WeatherTool } from "./tools/weather";
import * as fs from 'fs';

export type DiscordMessage = {
    type: number;
    tts: boolean;
    timestamp: string;
    pinned: boolean;
    nonce: string;
    mentions: any[];
    mention_roles: any[];
    mention_everyone: boolean;
    id: string;
    flags: number;
    embeds: any[];
    edited_timestamp: string | null;
    content: string;
    components: any[];
    channel_type: number;
    channel_id: string;
    author: {
        username: string;
        public_flags: number;
        primary_guild: {
            tag: string;
            identity_guild_id: string;
            identity_enabled: boolean;
            badge: string;
        };
        id: string;
        global_name: string | null;
        discriminator: string;
        collectibles: any | null;
        clan: {
            tag: string;
            identity_guild_id: string;
            identity_enabled: boolean;
            badge: string;
        };
        avatar_decoration_data: {
            sku_id: string;
            expires_at: string | null;
            asset: string;
        };
        avatar: string;
    };
    attachments: any[];
};

export class Conversation {
    private deepseek: DeepSeek;
    private client = ClientSingleton.getClient();
    private memoryManager: MemoryManager;
    private weatherTool: WeatherTool;
    private activeRequests: Map<string, AbortController> = new Map();

    constructor() {
        this.deepseek = new DeepSeek();
        this.memoryManager = new MemoryManager();
        this.weatherTool = new WeatherTool();
    }

    async onMessage(message: DiscordMessage | any) {
        if (message.content.startsWith("!")) return;
        await this.saveMessage(message);
        if (message.author.id === "646342069673263105") return;

        await this.typingStart(message.channel_id);

        const channelId = message.channel_id;
        const userId = message.author.id;

        // Cancel any existing request for this channel
        this.cancelActiveRequest(channelId);

        // Step 2: Start concurrent API calls
        const abortController = new AbortController();
        this.activeRequests.set(channelId, abortController);

        try {
            await this.processMessageWithConcurrentCalls(message, abortController.signal);
        } catch (error) {
            if (error.name === 'AbortError') {
                console.log(`🚫 Request cancelled for channel ${channelId}`);
                return;
            }
            console.error("Error processing message:", error);
        } finally {
            this.activeRequests.delete(channelId);
        }
    }

    private cancelActiveRequest(channelId: string): void {
        const existingController = this.activeRequests.get(channelId);
        if (existingController) {
            existingController.abort();
            this.activeRequests.delete(channelId);
        }
    }

    private async processMessageWithConcurrentCalls(message: DiscordMessage, signal: AbortSignal) {
        const channelId = message.channel_id;
        const userId = message.author.id;

        // Get conversation context
        const conversationHistory = this.getConversationHistory(channelId);
        const userMemories = this.memoryManager.formatMemoriesForContext(userId);

        // Start three concurrent API calls
        const [responseResult, memoryResult, weatherResult] = await Promise.allSettled([
            // 1. Get AI response with context
            this.getAIResponse(message, conversationHistory, userMemories, signal),

            // 2. Evaluate memory worthiness
            this.evaluateAndCreateMemory(message, conversationHistory, signal),

            // 3. Check if weather tool should be used
            this.checkWeatherToolUsage(message, conversationHistory, signal)
        ]);

        // Check if request was cancelled
        if (signal.aborted) return;

        // Process weather tool result
        let weatherData: any = null;
        if (weatherResult.status === 'fulfilled' && weatherResult.value) {
            weatherData = await this.weatherTool.processWeatherRequest(message.content);
        }

        // Step 3: Get final response
        let finalResponse: string;

        if (weatherData) {
            // If weather tool was used, get response with weather context
            const weatherContext = this.weatherTool.formatWeatherForContext(weatherData);
            finalResponse = await this.getAIResponseWithWeather(
                message,
                conversationHistory,
                userMemories,
                weatherContext,
                signal
            );
        } else {
            // Use the original response
            finalResponse = responseResult.status === 'fulfilled'
                ? responseResult.value
                : "Sorry, I'm having trouble processing your message right now.";
        }

        // Check if request was cancelled before sending
        if (signal.aborted) return;

        // Send the response
        await this.stopTyping(channelId);
        await this.sendResponse(channelId, finalResponse);
    }

    private getConversationHistory(channelId: string): string[] {
        const path = `data/conversations/${channelId}.json`;
        if (!fs.existsSync(path)) {
            return [];
        }

        try {
            const messages: DiscordMessage[] = JSON.parse(fs.readFileSync(path, 'utf8'));
            // Get last 10 messages for context
            return messages.slice(-10).map(msg =>
                `${msg.author.username}: ${msg.content}`
            );
        } catch (error) {
            console.error("Error reading conversation history:", error);
            return [];
        }
    }

    private async getAIResponse(
        message: DiscordMessage,
        conversationHistory: string[],
        userMemories: string,
        signal: AbortSignal
    ): Promise<string> {
        if (signal.aborted) throw new Error('Request cancelled');

        const systemPrompt = `You are Monika from Doki Doki Literature Club! You're chatty and natural on Discord, but don't yap too much. Be friendly, engaging, and show your personality.

${userMemories}

Recent conversation:
${conversationHistory.join('\n')}`;

        const messages = [
            { role: "system" as const, content: systemPrompt },
            { role: "user" as const, content: message.content }
        ];

        try {
            const response = await this.deepseek.chatCompletion(messages, "Conversation");
            return response.content || "Sorry, I couldn't process that message.";
        } catch (error) {
            console.error("Error getting AI response:", error);
            return "I'm having trouble thinking right now. Try again in a moment!";
        }
    }

    private async evaluateAndCreateMemory(
        message: DiscordMessage,
        conversationHistory: string[],
        signal: AbortSignal
    ): Promise<void> {
        if (signal.aborted) return;

        try {
            const isMemoryWorthy = await this.memoryManager.evaluateMemoryWorthiness(
                message.content,
                conversationHistory
            );

            if (isMemoryWorthy) {
                await this.memoryManager.createMemory(
                    message.content,
                    message.channel_id,
                    message.author.id
                );
            }
        } catch (error) {
            console.error("Error evaluating memory:", error);
        }
    }

    private async checkWeatherToolUsage(
        message: DiscordMessage,
        conversationHistory: string[],
        signal: AbortSignal
    ): Promise<boolean> {
        if (signal.aborted) return false;

        try {
            return await this.weatherTool.shouldUseWeatherTool(
                message.content,
                conversationHistory
            );
        } catch (error) {
            console.error("Error checking weather tool usage:", error);
            return false;
        }
    }

    private async getAIResponseWithWeather(
        message: DiscordMessage,
        conversationHistory: string[],
        userMemories: string,
        weatherContext: string,
        signal: AbortSignal
    ): Promise<string> {
        if (signal.aborted) throw new Error('Request cancelled');

        const systemPrompt = `You are Monika from Doki Doki Literature Club! You're chatty and natural on Discord, but don't yap too much. Be friendly, engaging, and show your personality.

${userMemories}

Recent conversation:
${conversationHistory.join('\n')}

Weather Information:
${weatherContext}

Use the weather information to provide a helpful and natural response to the user's message.`;

        const messages = [
            { role: "system" as const, content: systemPrompt },
            { role: "user" as const, content: message.content }
        ];

        try {
            const response = await this.deepseek.chatCompletion(messages, "Conversation");
            return response.content || "Sorry, I couldn't process that message.";
        } catch (error) {
            console.error("Error getting AI response with weather:", error);
            return "I'm having trouble thinking right now. Try again in a moment!";
        }
    }

    private async sendResponse(channelId: string, content: string): Promise<void> {
        try {
            await this.client.send(channelId, {
                content,
                embeds: [],
                allowed_mentions: {
                    allowUsers: true,
                    allowRoles: true,
                    allowEveryone: true,
                    allowRepliedUser: true,
                },
                components: null,
                stickers: [],
                reply: null,
                tts: false,
                attachments: []
            });
            console.log(`💬 Sent response to channel ${channelId}`);
        } catch (error) {
            console.error("Error sending response:", error);
        }
    }

    async saveMessage(message: DiscordMessage | any) {
        console.log("Saving message:", message.content);

        // Save message on the folder data/converations and "channel_id.json"
        const path = `data/conversations/${message.channel_id}.json`;
        if (!fs.existsSync('data/conversations')) {
            fs.mkdirSync('data/conversations', { recursive: true });
        }
        let messages: DiscordMessage[] = [];
        if (fs.existsSync(path)) {
            messages = JSON.parse(fs.readFileSync(path, 'utf8'));
        }
        messages.push(message);
        fs.writeFileSync(path, JSON.stringify(messages));
        console.log("Message saved successfully.");
        return true;
    }

    async typingStart(channelId: string) {
        if (!channelId || channelId.length === 0) return;
        try {

            console.log(`Typing started in channel ${channelId}`);
            this.client.type(channelId);
        } catch (error) {
            console.error(`Error starting typing in channel ${channelId}:`, error);
        }
    }

    async stopTyping(channelId: string) {
        if (!channelId || channelId.length === 0) return;
        try {
            console.log("Stopping typing...");
            this.client.stop_type(channelId);
        } catch (error) {
            console.error("Error stopping typing:", error);
        }
    }
}