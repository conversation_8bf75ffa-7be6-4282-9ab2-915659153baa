import OpenAI from "openai";

export interface AiMessage {
    role: 'user' | 'assistant' | 'system';
    content: string;
}

export class DeepSeek {
  private openai: OpenAI;

  constructor() {
    this.openai = new OpenAI({
        baseURL: 'https://api.deepseek.com/v1',
        apiKey: process.env.DEEPSEEK_API_KEY || "",
    });
  }

  async chatCompletion(messages: AiMessage[], temperature: "Coding" | "Data Analysis" | "Conversation" | "Translation" | "Poetry" = "Conversation") {
    let te = 1.3;
    if (temperature === "Coding") te = 0;
    else if (temperature === "Data Analysis") te = 1;
    else if (temperature === "Conversation") te = 1.3;
    else if (temperature === "Translation") te = 1.3;
    else if (temperature === "Poetry") te = 1.5;
    
    const chatCompletion = await this.openai.chat.completions.create({
      messages: messages,
      model: "gpt-4",
      temperature: te
    });
    return chatCompletion.choices[0].message;
  }
}