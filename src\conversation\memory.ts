import { DeepSeek, AiMessage } from "../ai/deepseek";
import * as fs from 'fs';
import * as path from 'path';

export interface UserMemory {
    id: string;
    content: string;
    timestamp: string;
    channelId: string;
    userId: string;
}

export class MemoryManager {
    private deepseek: DeepSeek;
    private memoriesDir = 'data/memories';

    constructor() {
        this.deepseek = new DeepSeek();
        this.ensureMemoriesDirectory();
    }

    private ensureMemoriesDirectory(): void {
        if (!fs.existsSync(this.memoriesDir)) {
            fs.mkdirSync(this.memoriesDir, { recursive: true });
        }
    }

    private getMemoryFilePath(userId: string): string {
        return path.join(this.memoriesDir, `${userId}.json`);
    }

    async evaluateMemoryWorthiness(message: string, context: string[]): Promise<boolean> {
        const evaluationPrompt = `
You are evaluating whether a message contains information worth remembering about a user for future conversations.

Consider these criteria for memory-worthy content:
- Personal information (name, age, location, job, interests, hobbies)
- Preferences and opinions
- Important life events or experiences
- Relationships and social connections
- Goals, plans, or aspirations
- Emotional states or personality traits
- Skills or expertise
- Recurring topics or patterns

Message to evaluate: "${message}"

Recent context: ${context.join('\n')}

Respond with only "YES" if this message contains memory-worthy information, or "NO" if it doesn't.
        `.trim();

        const messages: AiMessage[] = [
            { role: "system", content: evaluationPrompt },
            { role: "user", content: message }
        ];

        try {
            const response = await this.deepseek.chatCompletion(messages, "Data Analysis");
            return response.content?.trim().toUpperCase() === "YES";
        } catch (error) {
            console.error("Error evaluating memory worthiness:", error);
            return false;
        }
    }

    async createMemory(message: string, channelId: string, userId: string): Promise<void> {
        const extractionPrompt = `
Extract the key information from this message that should be remembered about the user.
Focus on personal details, preferences, experiences, or other meaningful information.
Be concise but capture the essential details.

Message: "${message}"

Provide a brief, clear summary of what should be remembered:
        `.trim();

        const messages: AiMessage[] = [
            { role: "system", content: extractionPrompt },
            { role: "user", content: message }
        ];

        try {
            const response = await this.deepseek.chatCompletion(messages, "Data Analysis");
            const memoryContent = response.content?.trim();

            if (memoryContent) {
                const memory: UserMemory = {
                    id: this.generateMemoryId(),
                    content: memoryContent,
                    timestamp: new Date().toISOString(),
                    channelId,
                    userId
                };

                this.saveMemory(userId, memory);
                console.log(`💾 Created memory for user ${userId}: ${memoryContent}`);
            }
        } catch (error) {
            console.error("Error creating memory:", error);
        }
    }

    private generateMemoryId(): string {
        return `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    private saveMemory(userId: string, memory: UserMemory): void {
        const filePath = this.getMemoryFilePath(userId);
        let memories: UserMemory[] = [];

        if (fs.existsSync(filePath)) {
            try {
                const data = fs.readFileSync(filePath, 'utf8');
                memories = JSON.parse(data);
            } catch (error) {
                console.error("Error reading memories file:", error);
                memories = [];
            }
        }

        memories.push(memory);
        
        // Keep only the most recent 50 memories to prevent file bloat
        if (memories.length > 50) {
            memories = memories.slice(-50);
        }

        try {
            fs.writeFileSync(filePath, JSON.stringify(memories, null, 2));
        } catch (error) {
            console.error("Error saving memory:", error);
        }
    }

    getUserMemories(userId: string): UserMemory[] {
        const filePath = this.getMemoryFilePath(userId);
        
        if (!fs.existsSync(filePath)) {
            return [];
        }

        try {
            const data = fs.readFileSync(filePath, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            console.error("Error reading user memories:", error);
            return [];
        }
    }

    getRecentMemories(userId: string, limit: number = 10): UserMemory[] {
        const memories = this.getUserMemories(userId);
        return memories.slice(-limit);
    }

    formatMemoriesForContext(userId: string): string {
        const memories = this.getRecentMemories(userId);
        
        if (memories.length === 0) {
            return "No previous memories about this user.";
        }

        const memoryStrings = memories.map(memory => 
            `- ${memory.content} (${new Date(memory.timestamp).toLocaleDateString()})`
        );

        return `Previous memories about this user:\n${memoryStrings.join('\n')}`;
    }
}
