{"name": "moni-bot", "version": "1.0.0", "description": "A bot project for managing conversations and AI services.", "main": "main.ts", "scripts": {"start": "bun run main.ts", "dev": "bun run main.ts --watch", "test-typing": "bun run test-typing.ts"}, "keywords": ["bot", "ai", "conversations"], "author": "", "license": "MIT", "type": "module", "dependencies": {"axios": "^1.6.0", "discord-user-bots": "^2.0.3", "dotenv": "^16.5.0", "openai": "^5.5.1"}, "devDependencies": {}}